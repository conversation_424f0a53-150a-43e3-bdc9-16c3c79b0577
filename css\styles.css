/*
   Easybank Landing Page
   CSS Styles
*/

/* Variables */
:root {
  /* Primary Colors */
  --dark-blue: hsl(233, 26%, 24%);
  --lime-green: hsl(136, 65%, 51%);
  --bright-cyan: hsl(192, 70%, 51%);

  /* Neutral Colors */
  --grayish-blue: hsl(233, 8%, 62%);
  --light-grayish-blue: hsl(220, 16%, 96%);
  --very-light-gray: hsl(0, 0%, 98%);
  --white: hsl(0, 0%, 100%);

  /* Font sizes */
  --font-sm: 0.875rem;
  --font-med: 1.125rem; /* 18px */
  --font-lg: 1.25rem;
  --font-xl: 2.25rem;
  --font-xxl: 3.25rem;

  /* Spacing */
  --section-padding: 5rem 0;
}

/* Reset */
*, *::before, *::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* Base Styles */
html {
  font-size: 100%;
  scroll-behavior: smooth;
}

body {
  font-family: 'Public Sans', sans-serif;
  font-size: var(--font-med);
  font-weight: 300;
  color: var(--grayish-blue);
  line-height: 1.5;
  overflow-x: hidden;
  background-color: var(--very-light-gray);
}

/* Educational Disclaimer Styles */
.educational-disclaimer {
  background: linear-gradient(135deg, #ff6b6b, #ffa500);
  color: white;
  padding: 1rem 0;
  position: sticky;
  top: 0;
  z-index: 1000;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  border-bottom: 3px solid #ff4757;
}

.disclaimer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  text-align: center;
}

.educational-disclaimer h2 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: white;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.educational-disclaimer p {
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.4;
  color: white;
  max-width: 900px;
  margin: 0 auto;
}

.educational-disclaimer strong {
  font-weight: 700;
  text-decoration: underline;
}

a {
  text-decoration: none;
}

ul {
  list-style: none;
}

img {
  max-width: 100%;
  display: block;
}

.container {
  max-width: 1110px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

/* Utility Classes */
.flex {
  display: flex;
}

.flex-jc-sb {
  justify-content: space-between;
}

.flex-jc-c {
  justify-content: center;
}

.flex-ai-c {
  align-items: center;
}

.flex-column {
  flex-direction: column;
}

/* Button Styles */
.button {
  display: inline-block;
  padding: 0.875rem 2.25rem;
  background: linear-gradient(to right, var(--lime-green), var(--bright-cyan));
  border-radius: 50px;
  border: none;
  color: var(--white);
  font-weight: 700;
  font-size: var(--font-sm);
  cursor: pointer;
  transition: opacity 0.3s ease;
}

.button:hover {
  opacity: 0.7;
}

/* Header */
.header {
  position: relative;
  z-index: 1;
  background-color: var(--white);
}

.header__container {
  position: relative;
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}

.header__logo img {
  width: 139px;
  height: 20px;
}

.header__menu {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: linear-gradient(to bottom, rgba(45, 49, 77, 0.8), transparent);
  padding-top: 5rem;
  transform: translateX(100%);
  transition: transform 0.3s ease-in-out;
  z-index: 1;
}

.header__menu.active {
  transform: translateX(0);
}

.header__menu-container {
  background-color: var(--white);
  padding: 2rem;
  border-radius: 5px;
  margin: 0 1.5rem;
}

.header__links {
  flex-direction: column;
  text-align: center;
}

.header__links a {
  display: block;
  padding: 0.625rem;
  color: var(--dark-blue);
  font-weight: 400;
  transition: color 0.3s ease;
}

.header__links a:hover {
  color: var(--lime-green);
}

.header__toggle {
  cursor: pointer;
}

.header__toggle > span {
  display: block;
  width: 26px;
  height: 2px;
  background-color: var(--dark-blue);
  transition: all 0.3s ease-in-out;
  transform-origin: 3px 1px;
}

.header__toggle > span:not(:last-child) {
  margin-bottom: 5px;
}

.header__toggle.active > span:first-child {
  transform: rotate(45deg);
}

.header__toggle.active > span:nth-child(2) {
  opacity: 0;
}

.header__toggle.active > span:last-child {
  transform: rotate(-45deg);
}

.header__cta {
  display: none;
}

/* Hide mobile menu toggle on desktop */
.header__toggle {
  display: block;
}

/* Hero Section */
.hero {
  position: relative;
  background-color: var(--very-light-gray);
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  background-image: url('../images/bg-intro-mobile.svg');
  background-size: cover;
  background-position: center top;
  background-repeat: no-repeat;
}

.hero__container {
  position: relative;
  padding: 0;
  text-align: center;
}

.hero__image {
  position: relative;
  margin-top: -3rem;
  margin-bottom: 2.5rem;
}

.hero__image img {
  position: relative;
  width: 100%;
  max-width: 100%;
  transform: translateY(-3rem);
}

.hero__text {
  padding: 0 1.5rem 5rem;
}

.hero__text h1 {
  font-size: var(--font-xl);
  font-weight: 300;
  color: var(--dark-blue);
  line-height: 1.2;
  margin-bottom: 1.5rem;
}

.hero__text p {
  margin-bottom: 2rem;
}

/* Features Section */
.features {
  background-color: var(--light-grayish-blue);
  padding: var(--section-padding);
  text-align: center;
  position: relative;
  z-index: 1;
}

.features__intro {
  margin-bottom: 3.5rem;
}

.features__intro h2 {
  font-size: var(--font-xl);
  font-weight: 300;
  color: var(--dark-blue);
  margin-bottom: 1.5rem;
}

.features__grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
}

.feature {
  margin-bottom: 1.5rem;
}

.feature img {
  margin: 0 auto 1.5rem;
}

.feature h3 {
  font-size: var(--font-lg);
  font-weight: 300;
  color: var(--dark-blue);
  margin-bottom: 1rem;
}

/* Articles Section */
.articles {
  background-color: var(--very-light-gray);
  padding: var(--section-padding);
}

.articles h2 {
  font-size: var(--font-xl);
  font-weight: 300;
  color: var(--dark-blue);
  margin-bottom: 2.5rem;
  text-align: center;
}

.articles__grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

.article {
  background-color: var(--white);
  border-radius: 5px;
  overflow: hidden;
  box-shadow: 0px 3px 12px rgba(0, 0, 0, 0.05);
}

.article img {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.article__content {
  padding: 1.5rem;
}

.article__author {
  font-size: var(--font-sm);
  color: var(--grayish-blue);
  margin-bottom: 0.5rem;
  display: block;
}

.article__content h3 {
  font-size: var(--font-lg);
  font-weight: 300;
  margin-bottom: 0.5rem;
}

.article__content h3 a {
  color: var(--dark-blue);
  transition: color 0.3s ease;
}

.article__content h3 a:hover {
  color: var(--lime-green);
}

.article__content p {
  font-size: var(--font-sm);
}

/* Footer */
.footer {
  background-color: var(--dark-blue);
  padding: 2.5rem 0;
  text-align: center;
}

.footer__container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.footer__logo {
  margin-bottom: 2rem;
  display: inline-block;
}

.footer__logo img {
  width: 139px;
  height: auto;
}

.footer__social-links {
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
}

.footer__social-links a {
  margin: 0 0.75rem 0 0;
}

.footer__social-links a:hover img {
  filter: brightness(0) saturate(100%) invert(80%) sepia(32%) saturate(464%) hue-rotate(95deg) brightness(91%) contrast(85%);
}

.footer__links {
  display: flex;
  flex-direction: column;
  margin-bottom: 2rem;
}

.footer__links a {
  color: var(--white);
  padding: 0.5rem 0;
  transition: color 0.3s ease;
}

.footer__links a:hover {
  color: var(--lime-green);
}

.footer__cta {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.footer__cta .button {
  margin-bottom: 1.5rem;
}

.footer__cta p {
  font-size: var(--font-sm);
}

.attribution {
  background-color: var(--dark-blue);
  color: var(--white);
  text-align: center;
  padding-bottom: 1rem;
  font-size: var(--font-sm);
}

.attribution a {
  color: var(--lime-green);
}

/* No-scroll utility class */
.no-scroll {
  overflow: hidden;
}

/* Media Queries */
@media (min-width: 768px) {
  .header__toggle {
    display: none;
  }

  .header__menu {
    position: static;
    background: none;
    padding-top: 0;
    transform: translateX(0);
  }

  .header__menu-container {
    background-color: transparent;
    padding: 0;
    margin: 0;
  }

  .header__links {
    flex-direction: row;
  }

  .header__links a {
    position: relative;
    padding: 0 1rem;
  }

  .header__links a::after {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    bottom: -30px;
    height: 4px;
    background: linear-gradient(to right, var(--lime-green), var(--bright-cyan));
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .header__links a:hover::after {
    opacity: 1;
  }

  .header__cta {
    display: block;
  }

  /* Hero section - desktop */
  .hero {
    min-height: 650px;
  }

  .hero::before {
    background-image: url('../images/bg-intro-desktop.svg');
    background-position: 0% 83%;
    background-size: 120%;
    left: 40%;
  }

  .hero__container {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding-top: 0;
    text-align: left;
  }

  .hero__image {
    flex: 1;
    position: relative;
    margin: 0;
    transform: translateY(-5%);
  }

  .hero__image img {
    position: absolute;
    width: 120%;
    max-width: none;
    transform: translate(-10%, -12%);
    right: -10%;
  }

  .hero__text {
    flex: 1;
    padding: 5rem 0;
    padding-right: 6rem;
  }

  .hero__text h1 {
    font-size: var(--font-xxl);
  }

  /* Features section - desktop */
  .features {
    text-align: left;
  }

  .features__grid {
    grid-template-columns: repeat(4, 1fr);
  }

  /* Articles section - desktop */
  .articles h2 {
    text-align: left;
  }

  .articles__grid {
    grid-template-columns: repeat(4, 1fr);
  }

  /* Footer - desktop */
  .footer__container {
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
    text-align: left;
  }

  .footer__social {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
  }

  .footer__logo {
    margin-bottom: 3rem;
    background-color: rgba(245, 245, 245, 0.911);
  }

  .footer__logo img {
    width: 139px;
    height: 20px;
  }

  .footer__social-links {
    margin-bottom: 0;
    justify-content: flex-start;
  }

  .footer__links {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(3, auto);
    gap: 1rem;
    margin-bottom: 0;
    text-align: left;
  }

  .footer__links a {
    padding: 0;
  }

  .footer__cta {
    align-items: flex-end;
  }
}
